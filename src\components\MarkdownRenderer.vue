<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script>
import { marked } from 'marked'
import hljs from 'highlight.js/lib/core'
// 导入常用的语言支持
import javascript from 'highlight.js/lib/languages/javascript'
import java from 'highlight.js/lib/languages/java'
import python from 'highlight.js/lib/languages/python'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import sql from 'highlight.js/lib/languages/sql'
import json from 'highlight.js/lib/languages/json'

// 注册语言
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('java', java)
hljs.registerLanguage('python', python)
hljs.registerLanguage('css', css)
hljs.registerLanguage('html', html)
hljs.registerLanguage('xml', html)
hljs.registerLanguage('sql', sql)
hljs.registerLanguage('json', json)

export default {
  name: 'MarkdownRenderer',
  props: {
    // Markdown内容
    content: {
      type: String,
      default: ''
    },
    // 是否启用代码高亮
    enableHighlight: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    renderedContent() {
      if (!this.content) return ''
      
      try {
        // 配置marked选项
        const renderer = new marked.Renderer()
        
        // 自定义代码块渲染
        renderer.code = (code, language) => {
          if (this.enableHighlight && language && hljs.getLanguage(language)) {
            try {
              const highlighted = hljs.highlight(code, { language }).value
              return `<pre class="hljs"><code class="language-${language}">${highlighted}</code></pre>`
            } catch (err) {
              console.warn('代码高亮失败:', err)
            }
          }
          return `<pre><code class="language-${language || 'text'}">${this.escapeHtml(code)}</code></pre>`
        }
        
        // 自定义行内代码渲染
        renderer.codespan = (code) => {
          return `<code class="inline-code">${this.escapeHtml(code)}</code>`
        }
        
        // 自定义标题渲染
        renderer.heading = (text, level) => {
          const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-')
          return `<h${level} class="markdown-heading markdown-h${level}" id="${escapedText}">${text}</h${level}>`
        }
        
        // 自定义列表渲染
        renderer.list = (body, ordered) => {
          const type = ordered ? 'ol' : 'ul'
          return `<${type} class="markdown-list">${body}</${type}>`
        }
        
        // 自定义列表项渲染
        renderer.listitem = (text) => {
          return `<li class="markdown-list-item">${text}</li>`
        }
        
        // 自定义段落渲染
        renderer.paragraph = (text) => {
          return `<p class="markdown-paragraph">${text}</p>`
        }
        
        // 自定义强调渲染
        renderer.strong = (text) => {
          return `<strong class="markdown-strong">${text}</strong>`
        }
        
        // 自定义斜体渲染
        renderer.em = (text) => {
          return `<em class="markdown-em">${text}</em>`
        }
        
        // 配置marked
        marked.setOptions({
          renderer: renderer,
          gfm: true, // 启用GitHub风格的Markdown
          breaks: true, // 支持换行符转换为<br>
          sanitize: false, // 不清理HTML（我们会手动处理）
          smartLists: true,
          smartypants: true
        })
        
        return marked(this.content)
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        return this.escapeHtml(this.content)
      }
    }
  },
  methods: {
    // HTML转义函数
    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      }
      return text.replace(/[&<>"']/g, (m) => map[m])
    }
  }
}
</script>

<style scoped>
/* Markdown渲染器样式 */
.markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary);
  word-wrap: break-word;
}

/* 标题样式 */
.markdown-heading {
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary);
}

.markdown-h1 {
  font-size: 1.8em;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 8px;
}

.markdown-h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 6px;
}

.markdown-h3 {
  font-size: 1.3em;
  color: var(--primary-color);
}

.markdown-h4 {
  font-size: 1.1em;
  color: var(--primary-color);
}

.markdown-h5, .markdown-h6 {
  font-size: 1em;
  color: var(--text-secondary);
}

/* 段落样式 */
.markdown-paragraph {
  margin: 12px 0;
  line-height: 1.7;
}

/* 列表样式 */
.markdown-list {
  margin: 12px 0;
  padding-left: 24px;
}

.markdown-list-item {
  margin: 6px 0;
  line-height: 1.6;
}

/* 强调样式 */
.markdown-strong {
  font-weight: 700;
  color: var(--primary-color);
}

.markdown-em {
  font-style: italic;
  color: var(--text-secondary);
}

/* 行内代码样式 */
.inline-code {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 代码块样式 */
.markdown-renderer pre {
  background: #f8f9fa;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.5;
}

.markdown-renderer pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
}

/* 代码高亮样式 */
.hljs {
  background: #f8f9fa !important;
  color: #333;
}


</style>
