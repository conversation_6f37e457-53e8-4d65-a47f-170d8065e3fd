<template>
  <div class="recharge-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-wallet"></i>
          账户充值
        </h1>
        <p class="page-subtitle">为您的账户充值，享受更多AI服务</p>
      </div>
    </div>

    <!-- 当前余额显示 -->
    <div class="balance-card">
      <div class="balance-info">
        <div class="balance-label">当前余额</div>
        <div class="balance-amount">¥{{ balanceYuan }}</div>
      </div>
      <div class="balance-actions">
        <el-button type="text" @click="$router.push('/main/transactions')">
          <i class="el-icon-document"></i>
          查看明细
        </el-button>
      </div>
    </div>

    <div class="recharge-content">
      <!-- 左侧主要内容 -->
      <div class="main-content">
        <!-- 快速充值金额 -->
        <div class="recharge-section">
          <h3 class="section-title">选择充值金额</h3>
          <div class="amount-grid">
            <div
              v-for="amount in quickAmounts"
              :key="amount.value"
              class="amount-card"
              :class="{ active: selectedAmount === amount.value }"
              @click="selectAmount(amount.value)"
            >
              <div class="amount-value">¥{{ amount.value }}</div>
              <div v-if="amount.bonus" class="amount-bonus">
                送¥{{ amount.bonus }}
              </div>
            </div>
          </div>
        </div>

        <!-- 自定义金额 -->
        <div class="recharge-section">
          <h3 class="section-title">自定义金额</h3>
          <div class="custom-amount">
            <el-input
              v-model.number="customAmount"
              placeholder="请输入充值金额"
              type="number"
              :min="10"
              :max="10000"
              @input="selectCustomAmount"
            >
              <template slot="prepend">¥</template>
              <template slot="append">元</template>
            </el-input>
            <div class="amount-tips">
              <span>最低充值金额：¥10</span>
              <span>最高充值金额：¥10,000</span>
            </div>
          </div>
        </div>

        <!-- 支付方式选择按钮 -->
        <div class="recharge-section">
          <h3 class="section-title">支付方式</h3>
          <div class="payment-selector">
            <el-button
              type="primary"
              size="large"
              class="payment-select-btn"
              @click="showPaymentDialog = true"
            >
              <div class="payment-content">
                <img
                  v-if="selectedPaymentMethodInfo.image"
                  :src="selectedPaymentMethodInfo.image"
                  :alt="selectedPaymentMethodInfo.name"
                  class="payment-icon-img"
                />
                <i v-else :class="selectedPaymentMethodInfo.icon"></i>
                <span class="payment-text">{{ selectedPaymentMethodInfo.name }}</span>
              </div>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧信息栏 -->
      <div class="sidebar-content">
        <!-- 充值摘要 -->
        <div class="recharge-summary-card">
          <h4 class="summary-title">充值摘要</h4>
          <div class="summary-content">
            <div class="summary-item">
              <span>充值金额：</span>
              <span class="amount">¥{{ finalAmount }}</span>
            </div>
            <div v-if="bonusAmount > 0" class="summary-item bonus">
              <span>赠送金额：</span>
              <span class="amount">¥{{ bonusAmount }}</span>
            </div>
            <div class="summary-item total">
              <span>实际到账：</span>
              <span class="amount">¥{{ totalAmount }}</span>
            </div>
          </div>

          <el-button
            type="primary"
            size="large"
            class="recharge-btn"
            :disabled="!canRecharge"
            :loading="recharging"
            @click="confirmRecharge"
          >
            <i class="el-icon-wallet"></i>
            立即充值
          </el-button>
        </div>

        <!-- 优惠信息（可折叠） -->
        <div class="promotion-card-compact">
          <div class="promotion-header" @click="showPromotions = !showPromotions">
            <i class="el-icon-present"></i>
            <span>充值优惠</span>
            <i :class="showPromotions ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </div>
          <el-collapse-transition>
            <div v-show="showPromotions" class="promotion-content">
              <ul class="promotion-list-compact">
                <li>首次充值享受9.5折优惠</li>
                <li>单次充值满¥200送¥20</li>
                <li>单次充值满¥500送¥60</li>
                <li>VIP用户充值享受额外5%奖励</li>
              </ul>
            </div>
          </el-collapse-transition>
        </div>
      </div>
    </div>

    <!-- 支付方式选择模态框 -->
    <el-dialog
      title="选择支付方式"
      :visible.sync="showPaymentDialog"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="payment-methods-dialog">
        <div
          v-for="method in paymentMethods"
          :key="method.id"
          class="payment-method-dialog"
          :class="{ active: selectedPaymentMethod === method.id }"
          @click="selectPaymentMethod(method.id)"
        >
          <div class="method-icon">
            <img
              v-if="method.image"
              :src="method.image"
              :alt="method.name"
              class="payment-method-img"
            />
            <i v-else :class="method.icon"></i>
          </div>
          <div class="method-info">
            <div class="method-name">
              {{ method.name }}
              <span v-if="method.recommended" class="recommended-badge">推荐</span>
            </div>
            <div class="method-desc">{{ method.description }}</div>
          </div>
          <div class="method-radio">
            <el-radio
              v-model="selectedPaymentMethod"
              :label="method.id"
            ></el-radio>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showPaymentDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmPaymentMethod">确定</el-button>
      </div>
    </el-dialog>

    <!-- 最近充值记录（简化版） -->
    <div class="recent-records-compact">
      <div class="records-header">
        <h4>最近充值记录</h4>
        <el-button type="text" @click="$router.push('/main/transactions')">
          查看全部
        </el-button>
      </div>
      <div v-if="recentRecords.length === 0" class="no-records-compact">
        <span>暂无充值记录</span>
      </div>
      <div v-else class="records-list-compact">
        <div
          v-for="record in recentRecords.slice(0, 3)"
          :key="record.id"
          class="record-item-compact"
        >
          <div class="record-amount">+¥{{ (record.amount / 100).toFixed(2) }}</div>
          <div class="record-time">{{ formatTime(record.createTime) }}</div>
          <div class="record-status" :class="record.status">
            {{ getStatusText(record.status) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getRechargeList,getAliPayCode } from '@/api/vip'
export default {
  name: 'Recharge',
  data() {
    return {
      selectedAmount: 0,
      customAmount: '',
      selectedPaymentMethod: 'alipay',
      recharging: false,
      showPaymentDialog: false,
      showPromotions: false,
      quickAmounts: [
        { value: 50, bonus: 0 },
        { value: 100, bonus: 5 },
        { value: 200, bonus: 20 },
        { value: 500, bonus: 60 },
        { value: 1000, bonus: 150 },
        { value: 2000, bonus: 350 }
      ],
      aliPayCode:"",
      paymentMethods: [
        {
          id: 'alipay-code',
          name: '支付宝(电脑网站支付)',
          description: '安全便捷，支持花呗分期（推荐）',
          icon: 'el-icon-mobile-phone',
          image: require('@/assets/alipay.png'),
          recommended: false
        },
           {
          id: 'alipay',
          name: '支付宝(订单码支付)',
          description: '安全便捷，支持花呗分期（推荐）',
          icon: 'el-icon-mobile-phone',
          image: require('@/assets/alipay.png'),
          recommended: true
        },
        {
          id: 'wechat',
          name: '微信支付',
          description: '微信安全支付',
          icon: 'el-icon-chat-dot-round',
          image: require('@/assets/wechat.jpeg')
        },
        {
          id: 'bank',
          name: '银行卡',
          description: '支持各大银行储蓄卡',
          icon: 'el-icon-bank-card'
        }
      ],
      recentRecords: []
    }
  },
  computed: {
    ...mapGetters('user', ['balance', 'balanceYuan', 'isVip']),

    finalAmount() {
      return this.selectedAmount || this.customAmount || 0
    },

    bonusAmount() {
      const amount = this.finalAmount
      const quickAmount = this.quickAmounts.find(item => item.value === amount)
      if (quickAmount && quickAmount.bonus) {
        return quickAmount.bonus
      }

      // 计算充值奖励
      if (amount >= 500) return Math.floor(amount * 0.12)
      if (amount >= 200) return Math.floor(amount * 0.1)
      if (amount >= 100) return Math.floor(amount * 0.05)

      return 0
    },

    totalAmount() {
      return this.finalAmount + this.bonusAmount
    },

    canRecharge() {
      return this.finalAmount >= 10 && this.selectedPaymentMethod
    },

    selectedPaymentMethodInfo() {
      return this.paymentMethods.find(method => method.id === this.selectedPaymentMethod) || this.paymentMethods[0]
    }
  },
  async created() {
    await this.loadRecentRecords()
  },


    mounted(){
    this.initRechargeList();
  },
  methods: {
    //初始化充值列表相关
    initRechargeList(){
      getRechargeList().then(res=>{
        this.quickAmounts = res.data.quickRechargeOptionVoList
      })
    },

    ...mapActions('user', ['recharge']),
    
    selectAmount(amount) {
      this.selectedAmount = amount
      this.customAmount = ''
    },
    
    selectCustomAmount() {
      this.selectedAmount = 0
    },

    selectPaymentMethod(methodId) {
      this.selectedPaymentMethod = methodId
    },

    confirmPaymentMethod() {
      this.showPaymentDialog = false
    },

    initGetAliPayCode(){
      getAliPayCode().then(res=>{
        this.aliPayCode = res.data
      })
    },
    
    async confirmRecharge() {
      if (!this.canRecharge) return
      
      this.initGetAliPayCode();
       window.open(this.aliPayCode,'_ blank') //新窗口打开支付宝预支付页面

      // this.recharging = true
      
      // try {
      //   const result = await this.recharge({
      //     amount: this.finalAmount * 100, // 转换为分
      //     paymentMethod: this.selectedPaymentMethod
      //   })
        
      //   if (result.success) {
      //     this.$message.success('充值成功！')
      //     this.resetForm()
      //     await this.loadRecentRecords()
      //   } else {
      //     this.$message.error(result.message)
      //   }
      // } catch (error) {
      //   this.$message.error('充值失败，请重试')
      // } finally {
      //   this.recharging = false
      // }
    },
    
    resetForm() {
      this.selectedAmount = 0
      this.customAmount = ''
    },
    
    async loadRecentRecords() {
      // 模拟加载最近充值记录
      this.recentRecords = [
        {
          id: '1',
          amount: 10000,
          paymentMethod: '支付宝',
          createTime: new Date().toISOString(),
          status: 'success'
        }
      ]
    },
    
    formatTime(timeString) {
      return new Date(timeString).toLocaleString('zh-CN')
    },
    
    getStatusText(status) {
      const statusMap = {
        success: '成功',
        pending: '处理中',
        failed: '失败'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.recharge-container {
  width: 95%;
  max-width: 2000px;
  margin: 0 auto;
  padding: 30px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;

  .page-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 10px;

    i {
      color: #f39c12;
      margin-right: 12px;
    }
  }

  .page-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;

  .balance-label {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 5px;
  }

  .balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
  }
}

// 新的布局样式
.recharge-content {
  display: grid;
  grid-template-columns: 1fr 450px;
  gap: 50px;
  align-items: start;
}

.main-content {
  .recharge-section {
    margin-bottom: 40px;

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 25px;
    }
  }
}

.sidebar-content {
  position: sticky;
  top: 20px;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.amount-card {
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 25px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  &:hover {
    border-color: var(--primary-color);
  }
  
  &.active {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
  }
  
  .amount-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
  }

  .amount-bonus {
    font-size: 1rem;
    color: #e74c3c;
    font-weight: 600;
  }
}

.custom-amount {
  .el-input {
    .el-input__inner {
      height: 50px;
      font-size: 1.1rem;
    }
  }

  .amount-tips {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    font-size: 1rem;
    color: var(--text-secondary);
  }
}

// 支付方式选择按钮
.payment-selector {
  .payment-select-btn {
    width: 100%;
    height: 55px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    padding: 0 25px;
    border-radius: 12px;
    background: var(--bg-primary) !important;
    border: 2px solid var(--border-color) !important;
    color: var(--text-primary) !important;

    &:hover {
      background: var(--bg-secondary) !important;
      border-color: var(--primary-color) !important;
    }

    .payment-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
    }

    .payment-icon-img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      border-radius: 4px;
      object-fit: contain;
      flex-shrink: 0;
    }

    i:first-child {
      margin-right: 12px;
      font-size: 1.3rem;
      flex-shrink: 0;
    }

    .payment-text {
      text-align: center;
    }
  }
}

// 支付方式模态框样式
.payment-methods-dialog {
  .payment-method-dialog {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 18px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 12px;
    background: var(--bg-primary);

    &:hover {
      border-color: var(--primary-color);
      background: var(--bg-secondary);
    }

    &.active {
      border-color: var(--primary-color);
      background: rgba(37, 99, 235, 0.05);
    }

    .method-icon {
      font-size: 1.5rem;
      color: var(--primary-color);
      margin-right: 18px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .payment-method-img {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        object-fit: contain;
      }
    }

    .method-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .method-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 5px;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 8px;

        .recommended-badge {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          color: white;
          font-size: 0.75rem;
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;
          box-shadow: 0 2px 4px rgba(238, 90, 36, 0.3);
        }
      }

      .method-desc {
        font-size: 0.9rem;
        color: var(--text-secondary);
      }
    }
  }
}

// 侧边栏样式
.recharge-summary-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 25px;
  box-shadow: var(--shadow-md);

  .summary-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 25px;
    text-align: center;
  }

  .summary-content {
    margin-bottom: 30px;

    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      font-size: 1.1rem;

      &.bonus .amount {
        color: #e74c3c;
      }

      &.total {
        border-top: 1px solid var(--border-color);
        padding-top: 15px;
        font-weight: 700;
        font-size: 1.3rem;

        .amount {
          color: var(--primary-color);
        }
      }
    }
  }

  .recharge-btn {
    width: 100%;
    height: 55px;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 12px;
  }
}

.promotion-card-compact {
  background: var(--bg-primary);
  border: 2px solid #f39c12;
  border-radius: 12px;
  overflow: hidden;

  .promotion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px;
    background: rgba(243, 156, 18, 0.1);
    cursor: pointer;
    font-weight: 700;
    color: #f39c12;
    font-size: 1.1rem;

    i:first-child {
      margin-right: 10px;
      font-size: 1.2rem;
    }
  }

  .promotion-content {
    padding: 18px;

    .promotion-list-compact {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: 8px 0;
        color: var(--text-primary);
        font-size: 1rem;

        &:before {
          content: "•";
          color: #f39c12;
          margin-right: 10px;
          font-size: 1.1rem;
        }
      }
    }
  }
}

// 简化的充值记录样式
.recent-records-compact {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 25px;
  margin-top: 30px;
  box-shadow: var(--shadow-sm);

  .records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);

    h4 {
      font-size: 1.3rem;
      font-weight: 700;
      color: var(--text-primary);
      margin: 0;
    }
  }

  .no-records-compact {
    text-align: center;
    padding: 30px 20px;
    color: var(--text-secondary);
    font-size: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
  }

  .records-list-compact {
    .record-item-compact {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid var(--border-color);
      font-size: 1rem;

      &:last-child {
        border-bottom: none;
      }

      .record-amount {
        font-weight: 600;
        color: #27ae60;
      }

      .record-time {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .record-status {
        &.success {
          color: #27ae60;
        }

        &.pending {
          color: #f39c12;
        }

        &.failed {
          color: #e74c3c;
        }
      }
    }
  }
}




</style>
